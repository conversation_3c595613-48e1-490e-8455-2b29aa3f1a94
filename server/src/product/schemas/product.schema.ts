import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";

export type ProductDocument = Product & Document;

@Schema({ timestamps: true })
export class Product {
  @Prop({ required: true })
  name: string; // 商品名称

  @Prop({ required: true, unique: true })
  productNumber: string; // 商品编号（8位随机数）

  @Prop({ required: true, min: 0, max: 100 })
  alcoholContent: number; // 酒精度（百分比）

  @Prop({ required: true })
  packagingDate: Date; // 包装日期

  @Prop()
  description?: string; // 商品描述

  @Prop({ default: "active", enum: ["active", "inactive", "discontinued"] })
  status: string; // 商品状态

  @Prop({ default: "unverified", enum: ["unverified", "verified", "expired"] })
  verificationStatus: string; // 验证状态：未验证、已验证、已过期（保留用于兼容性，实际判断基于verifiedAt字段）

  @Prop()
  brand?: string; // 品牌

  @Prop()
  category?: string; // 分类

  @Prop()
  volume?: number; // 容量（毫升）

  @Prop()
  batchNumber?: string; // 批次号

  @Prop()
  productionLocation?: string; // 生产地

  // 验证相关字段
  @Prop()
  verifiedBy?: string; // 验证人手机号

  @Prop()
  verifiedAt?: Date; // 验证时间（主要判断字段：存在表示已验证，不存在表示未验证）

  @Prop()
  verificationLocation?: string; // 验证地点

  @Prop()
  verificationDevice?: string; // 验证设备信息

  @Prop({ default: Date.now })
  createdAt: Date; // 创建时间

  @Prop({ default: Date.now })
  updatedAt: Date; // 更新时间
}

export const ProductSchema = SchemaFactory.createForClass(Product);
