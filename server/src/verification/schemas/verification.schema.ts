import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from "mongoose";
import { VerificationLocation } from "./verification-location.schema"; // 引入子文档
import { VerificationDeviceInfo } from "./verification-device-info.schema"; // 引入子文档

export type VerificationDocument = Verification & Document;

@Schema({ timestamps: true })
export class Verification {
  @Prop({ required: true })
  productNumber: string;

  @Prop({ type: Types.ObjectId, ref: "Product", required: true })
  productId: Types.ObjectId;

  @Prop()
  userName?: string;

  @Prop()
  wechatOpenId?: string;

  @Prop()
  wechatUnionId?: string;

  @Prop({ required: true })
  verificationTime: Date;

  @Prop({ type: () => VerificationLocation }) // 使用子文档
  location?: VerificationLocation;

  @Prop({ type: () => VerificationDeviceInfo }) // 使用子文档
  deviceInfo?: VerificationDeviceInfo;

  @Prop()
  remarks?: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const VerificationSchema = SchemaFactory.createForClass(Verification);
