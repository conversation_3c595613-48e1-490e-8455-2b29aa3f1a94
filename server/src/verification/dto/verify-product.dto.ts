import { ApiProperty } from "@nestjs/swagger";
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
} from "class-validator";

export class VerifyProductDto {
  @ApiProperty({
    description: "商品编号（8位数字）",
    example: "12345678",
    pattern: "^\\d{8}$",
  })
  @IsString()
  @Matches(/^\d{8}$/, { message: "商品编号必须是8位数字" })
  productNumber: string;
}

export class QueryVerificationDto {
  @ApiProperty({ description: "页码", example: 1, required: false })
  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @ApiProperty({ description: "每页数量", example: 10, required: false })
  @IsOptional()
  @IsNumber()
  limit?: number = 10;

  @ApiProperty({ description: "商品编号搜索", required: false })
  @IsOptional()
  @IsString()
  productNumber?: string;

  @ApiProperty({
    description: "排序字段",
    enum: ["verificationTime", "createdAt"],
    default: "verificationTime",
    required: false,
  })
  @IsOptional()
  @IsEnum(["verificationTime", "createdAt"])
  sortBy?: string = "verificationTime";

  @ApiProperty({
    description: "排序方向",
    enum: ["asc", "desc"],
    default: "desc",
    required: false,
  })
  @IsOptional()
  @IsEnum(["asc", "desc"])
  sortOrder?: string = "desc";
}
