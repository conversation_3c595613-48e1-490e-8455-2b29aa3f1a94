import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { VerificationService } from './verification.service';
import { Product } from '../product/schemas/product.schema';
import { Verification } from './schemas/verification.schema';
import { NotFoundException, ConflictException } from '@nestjs/common';

describe('VerificationService', () => {
  let service: VerificationService;
  let productModel: any;
  let verificationModel: any;

  const mockProduct = {
    _id: 'product-id',
    productNumber: '12345678',
    name: 'Test Product',
    status: 'active',
    verifiedAt: null,
  };

  const mockVerifiedProduct = {
    ...mockProduct,
    verifiedAt: new Date('2024-01-01'),
  };

  beforeEach(async () => {
    const mockProductModel = {
      findOne: jest.fn(),
      findByIdAndUpdate: jest.fn(),
    };

    const mockVerificationModel = {
      create: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VerificationService,
        {
          provide: getModelToken(Product.name),
          useValue: mockProductModel,
        },
        {
          provide: getModelToken(Verification.name),
          useValue: mockVerificationModel,
        },
      ],
    }).compile();

    service = module.get<VerificationService>(VerificationService);
    productModel = module.get(getModelToken(Product.name));
    verificationModel = module.get(getModelToken(Verification.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('verifyProduct', () => {
    it('should throw NotFoundException when product does not exist', async () => {
      productModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(
        service.verifyProduct({ productNumber: '12345678' })
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException when product is already verified (verifiedAt exists)', async () => {
      productModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockVerifiedProduct),
      });

      verificationModel.create = jest.fn().mockResolvedValue({
        save: jest.fn().mockResolvedValue({}),
      });

      await expect(
        service.verifyProduct({ productNumber: '12345678' })
      ).rejects.toThrow(ConflictException);
    });

    it('should successfully verify product when verifiedAt is null', async () => {
      productModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockProduct),
      });

      const updatedProduct = {
        ...mockProduct,
        verifiedAt: new Date(),
        verificationStatus: 'verified',
      };

      productModel.findByIdAndUpdate.mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedProduct),
      });

      verificationModel.create = jest.fn().mockResolvedValue({
        save: jest.fn().mockResolvedValue({
          _id: 'verification-id',
          verificationTime: new Date(),
        }),
      });

      const result = await service.verifyProduct({ productNumber: '12345678' });

      expect(result.success).toBe(true);
      expect(result.message).toBe('验证成功');
      expect(productModel.findByIdAndUpdate).toHaveBeenCalledWith(
        mockProduct._id,
        expect.objectContaining({
          verificationStatus: 'verified',
          verifiedAt: expect.any(Date),
        }),
        { new: true }
      );
    });
  });

  describe('checkProductVerificationStatus', () => {
    it('should return verified status when verifiedAt exists', async () => {
      productModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockVerifiedProduct),
      });

      const result = await service.checkProductVerificationStatus('12345678');

      expect(result.success).toBe(true);
      expect(result.data.isVerified).toBe(true);
      expect(result.data.verificationTime).toBeDefined();
    });

    it('should return unverified status when verifiedAt is null', async () => {
      productModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockProduct),
      });

      const result = await service.checkProductVerificationStatus('12345678');

      expect(result.success).toBe(true);
      expect(result.data.isVerified).toBe(false);
      expect(result.data.verificationTime).toBeNull();
    });
  });
});
